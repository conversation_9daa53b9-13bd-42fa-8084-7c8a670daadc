import { OpenAI } from 'openai';
import { Message, Tool, ModelConfig, StreamingResponse } from '../../types';

export class DeepseekProvider {
  private client: OpenAI;

  constructor(apiKey: string) {
    this.client = new OpenAI({
      apiKey: apiKey,
      baseURL: 'https://api.deepseek.com/v1'
    });
  }

  async generateResponse(
    messages: Message[],
    tools: Tool[],
    config: ModelConfig
  ): Promise<StreamingResponse> {
    try {
      // Validate message sequence before conversion
      this.validateMessageSequence(messages);

      const openaiMessages = this.convertMessages(messages);
      const openaiTools = this.convertTools(tools);

      const response = await this.client.chat.completions.create({
        model: config.model,
        messages: openaiMessages,
        tools: openaiTools.length > 0 ? openaiTools : undefined,
        tool_choice: openaiTools.length > 0 ? 'auto' : undefined,
        temperature: config.temperature,
        max_tokens: config.maxTokens,
        top_p: config.topP,
        frequency_penalty: config.frequencyPenalty,
        presence_penalty: config.presencePenalty,
        stream: false
      });

      const choice = response.choices[0];
      if (!choice) {
        throw new Error('No response from Deepseek');
      }

      return {
        content: choice.message.content || '',
        done: true,
        toolCalls: choice.message.tool_calls?.map(tc => ({
          id: tc.id,
          type: 'function' as const,
          function: {
            name: tc.function.name,
            arguments: tc.function.arguments
          }
        }))
      };
    } catch (error) {
      throw new Error(`Deepseek API error: ${error}`);
    }
  }

  async streamResponse(
    messages: Message[],
    tools: Tool[],
    config: ModelConfig,
    onChunk: (chunk: string) => void
  ): Promise<void> {
    try {
      // Validate message sequence before conversion
      this.validateMessageSequence(messages);

      const openaiMessages = this.convertMessages(messages);
      const openaiTools = this.convertTools(tools);

      const stream = await this.client.chat.completions.create({
        model: config.model,
        messages: openaiMessages,
        tools: openaiTools.length > 0 ? openaiTools : undefined,
        tool_choice: openaiTools.length > 0 ? 'auto' : undefined,
        temperature: config.temperature,
        max_tokens: config.maxTokens,
        top_p: config.topP,
        frequency_penalty: config.frequencyPenalty,
        presence_penalty: config.presencePenalty,
        stream: true
      });

      for await (const chunk of stream) {
        const delta = chunk.choices[0]?.delta;
        if (delta?.content) {
          onChunk(delta.content);
        }
      }
    } catch (error) {
      throw new Error(`Deepseek streaming error: ${error}`);
    }
  }

  private convertMessages(messages: Message[]): any[] {
    return messages.map(msg => {
      switch (msg.role) {
        case 'system':
          return {
            role: 'system',
            content: msg.content
          };
        case 'user':
          return {
            role: 'user',
            content: msg.content
          };
        case 'assistant':
          const assistantMsg: any = {
            role: 'assistant',
            content: msg.content
          };

          if (msg.tool_calls) {
            assistantMsg.tool_calls = msg.tool_calls.map(tc => ({
              id: tc.id,
              type: 'function',
              function: {
                name: tc.function.name,
                arguments: tc.function.arguments
              }
            }));
          }

          return assistantMsg;
        case 'function':
          return {
            role: 'tool',
            tool_call_id: msg.name || 'unknown', // msg.name should contain the tool_call_id
            content: msg.content
          };
        default:
          throw new Error(`Unsupported message role: ${msg.role}`);
      }
    });
  }

  private convertTools(tools: Tool[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters
      }
    }));
  }

  private validateMessageSequence(messages: Message[]): void {
    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];

      // Check if tool result messages follow assistant messages with tool_calls
      if (message.role === 'function') {
        // Find the preceding assistant message with tool_calls
        let foundToolCall = false;
        for (let j = i - 1; j >= 0; j--) {
          const prevMessage = messages[j];
          if (prevMessage.role === 'assistant' && prevMessage.tool_calls) {
            // Check if this tool result corresponds to one of the tool calls
            const toolCallIds = prevMessage.tool_calls.map(tc => tc.id);
            if (toolCallIds.includes(message.name || '')) {
              foundToolCall = true;
              break;
            }
          }
          // Stop looking if we hit another assistant message without tool_calls
          if (prevMessage.role === 'assistant' && !prevMessage.tool_calls) {
            break;
          }
        }

        if (!foundToolCall) {
          console.warn(`Warning: Tool result message without corresponding tool call: ${message.name}`);
          // Don't throw error, just warn - let the API handle it
        }
      }
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.client.chat.completions.create({
        model: 'deepseek-chat',
        messages: [{ role: 'user', content: 'test' }],
        max_tokens: 10
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.models.list();
      return response.data.map(model => model.id).sort();
    } catch (error) {
      return ['deepseek-chat', 'deepseek-coder']; // fallback;
    }
  }
}
