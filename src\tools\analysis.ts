import * as fs from 'fs';
import * as path from 'path';
import { Tool, ToolResult } from '../types';

export class AnalysisTool implements Tool {
  name = 'analysis';
  description = 'Analyze code, files, and project structure. Can perform code quality analysis, dependency analysis, and project insights.';
  parameters = {
    type: 'object' as const,
    properties: {
      action: {
        type: 'string',
        enum: ['codebase_overview', 'file_analysis', 'dependency_analysis', 'error_analysis', 'performance_analysis'],
        description: 'The type of analysis to perform'
      },
      path: {
        type: 'string',
        description: 'Path to analyze (file or directory)'
      },
      language: {
        type: 'string',
        description: 'Programming language for analysis (optional)'
      },
      depth: {
        type: 'number',
        description: 'Analysis depth (1-5, default: 3)',
        default: 3
      },
      include_metrics: {
        type: 'boolean',
        description: 'Include code metrics in analysis',
        default: true
      }
    },
    required: ['action']
  };

  async execute(args: {
    action: string;
    path?: string;
    language?: string;
    depth?: number;
    include_metrics?: boolean;
  }): Promise<ToolResult> {
    try {
      const { action, path: targetPath = '.', language, depth = 3, include_metrics = true } = args;

      switch (action) {
        case 'codebase_overview':
          return await this.analyzeCodebase(targetPath, depth, include_metrics);
        case 'file_analysis':
          return await this.analyzeFile(targetPath, language, include_metrics);
        case 'dependency_analysis':
          return await this.analyzeDependencies(targetPath);
        case 'error_analysis':
          return await this.analyzeErrors(targetPath);
        case 'performance_analysis':
          return await this.analyzePerformance(targetPath, language);
        default:
          throw new Error(`Unknown analysis action: ${action}`);
      }
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: error.message || 'Analysis failed'
      };
    }
  }

  private async analyzeCodebase(targetPath: string, depth: number, includeMetrics: boolean): Promise<ToolResult> {
    const analysis: any = {
      overview: {},
      structure: {},
      languages: {},
      metrics: {},
      insights: []
    };

    try {
      // Get directory structure
      analysis.structure = await this.getDirectoryStructure(targetPath, depth);

      // Analyze languages
      analysis.languages = await this.analyzeLanguages(targetPath);

      // Get basic metrics
      if (includeMetrics) {
        analysis.metrics = await this.getCodeMetrics(targetPath);
      }

      // Generate insights
      analysis.insights = this.generateInsights(analysis);

      const output = this.formatCodebaseAnalysis(analysis);

      return {
        success: true,
        output,
        metadata: {
          action: 'codebase_overview',
          path: targetPath,
          analysis
        }
      };
    } catch (error) {
      throw new Error(`Codebase analysis failed: ${error}`);
    }
  }

  private async analyzeFile(filePath: string, language?: string, includeMetrics?: boolean): Promise<ToolResult> {
    if (!fs.existsSync(filePath)) {
      throw new Error(`File does not exist: ${filePath}`);
    }

    const stats = fs.statSync(filePath);
    if (stats.isDirectory()) {
      throw new Error(`Path is a directory, not a file: ${filePath}`);
    }

    const content = fs.readFileSync(filePath, 'utf-8');
    const detectedLanguage = language || this.detectLanguage(filePath);

    const analysis = {
      file: filePath,
      language: detectedLanguage,
      size: stats.size,
      lines: content.split('\n').length,
      characters: content.length,
      lastModified: stats.mtime,
      structure: this.analyzeFileStructure(content, detectedLanguage),
      complexity: includeMetrics ? this.calculateComplexity(content, detectedLanguage) : null,
      issues: this.findPotentialIssues(content, detectedLanguage)
    };

    const output = this.formatFileAnalysis(analysis);

    return {
      success: true,
      output,
      metadata: {
        action: 'file_analysis',
        path: filePath,
        analysis
      }
    };
  }

  private async analyzeDependencies(targetPath: string): Promise<ToolResult> {
    const dependencies: any = {
      packageFiles: [],
      dependencies: {},
      devDependencies: {},
      insights: []
    };

    // Look for package files
    const packageFiles = ['package.json', 'requirements.txt', 'Cargo.toml', 'go.mod', 'pom.xml'];

    for (const file of packageFiles) {
      const filePath = path.join(targetPath, file);
      if (fs.existsSync(filePath)) {
        dependencies.packageFiles.push(file);

        if (file === 'package.json') {
          const packageJson = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
          dependencies.dependencies = packageJson.dependencies || {};
          dependencies.devDependencies = packageJson.devDependencies || {};
        }
      }
    }

    dependencies.insights = this.generateDependencyInsights(dependencies);

    const output = this.formatDependencyAnalysis(dependencies);

    return {
      success: true,
      output,
      metadata: {
        action: 'dependency_analysis',
        path: targetPath,
        analysis: dependencies
      }
    };
  }

  private async analyzeErrors(targetPath: string): Promise<ToolResult> {
    // This is a simplified error analysis
    // In a real implementation, this would integrate with linters and static analysis tools

    const errorAnalysis: any = {
      commonIssues: [],
      suggestions: [],
      severity: 'info'
    };

    // Basic pattern matching for common issues
    const files = await this.getCodeFiles(targetPath);

    for (const file of files) {
      try {
        const content = fs.readFileSync(file, 'utf-8');
        const issues = this.findCommonIssues(content, file);
        errorAnalysis.commonIssues.push(...issues);
      } catch (error) {
        // Skip files that can't be read
      }
    }

    errorAnalysis.suggestions = this.generateErrorSuggestions(errorAnalysis.commonIssues);

    const output = this.formatErrorAnalysis(errorAnalysis);

    return {
      success: true,
      output,
      metadata: {
        action: 'error_analysis',
        path: targetPath,
        analysis: errorAnalysis
      }
    };
  }

  private async analyzePerformance(targetPath: string, language?: string): Promise<ToolResult> {
    const performanceAnalysis = {
      hotspots: [],
      recommendations: [],
      metrics: {}
    };

    // Basic performance analysis patterns
    const files = await this.getCodeFiles(targetPath);

    for (const file of files) {
      try {
        const content = fs.readFileSync(file, 'utf-8');
        const lang = language || this.detectLanguage(file);
        const hotspots = this.findPerformanceHotspots(content, lang, file);
        performanceAnalysis.hotspots.push(...hotspots);
      } catch (error) {
        // Skip files that can't be read
      }
    }

    performanceAnalysis.recommendations = this.generatePerformanceRecommendations(performanceAnalysis.hotspots);

    const output = this.formatPerformanceAnalysis(performanceAnalysis);

    return {
      success: true,
      output,
      metadata: {
        action: 'performance_analysis',
        path: targetPath,
        analysis: performanceAnalysis
      }
    };
  }

  // Helper methods
  private async getDirectoryStructure(dirPath: string, maxDepth: number): Promise<any> {
    const structure: any = {};

    if (maxDepth <= 0) return structure;

    try {
      const items = fs.readdirSync(dirPath);

      for (const item of items) {
        if (item.startsWith('.')) continue; // Skip hidden files

        const itemPath = path.join(dirPath, item);
        const stats = fs.statSync(itemPath);

        if (stats.isDirectory()) {
          structure[item] = await this.getDirectoryStructure(itemPath, maxDepth - 1);
        } else {
          structure[item] = {
            type: 'file',
            size: stats.size,
            extension: path.extname(item)
          };
        }
      }
    } catch (error) {
      // Handle permission errors gracefully
    }

    return structure;
  }

  private async analyzeLanguages(targetPath: string): Promise<Record<string, number>> {
    const languages: Record<string, number> = {};
    const files = await this.getCodeFiles(targetPath);

    for (const file of files) {
      const lang = this.detectLanguage(file);
      languages[lang] = (languages[lang] || 0) + 1;
    }

    return languages;
  }

  private async getCodeMetrics(targetPath: string): Promise<any> {
    const metrics = {
      totalFiles: 0,
      totalLines: 0,
      totalSize: 0,
      averageFileSize: 0
    };

    const files = await this.getCodeFiles(targetPath);
    metrics.totalFiles = files.length;

    for (const file of files) {
      try {
        const stats = fs.statSync(file);
        const content = fs.readFileSync(file, 'utf-8');

        metrics.totalSize += stats.size;
        metrics.totalLines += content.split('\n').length;
      } catch (error) {
        // Skip files that can't be read
      }
    }

    metrics.averageFileSize = metrics.totalFiles > 0 ? metrics.totalSize / metrics.totalFiles : 0;

    return metrics;
  }

  private async getCodeFiles(dirPath: string): Promise<string[]> {
    const files: string[] = [];
    const codeExtensions = ['.ts', '.js', '.tsx', '.jsx', '.py', '.java', '.cpp', '.c', '.cs', '.go', '.rs', '.php'];

    const walk = (dir: string) => {
      try {
        const items = fs.readdirSync(dir);

        for (const item of items) {
          if (item.startsWith('.') || item === 'node_modules') continue;

          const itemPath = path.join(dir, item);
          const stats = fs.statSync(itemPath);

          if (stats.isDirectory()) {
            walk(itemPath);
          } else if (codeExtensions.includes(path.extname(item))) {
            files.push(itemPath);
          }
        }
      } catch (error) {
        // Handle permission errors gracefully
      }
    };

    walk(dirPath);
    return files;
  }

  private detectLanguage(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const languageMap: Record<string, string> = {
      '.ts': 'TypeScript',
      '.tsx': 'TypeScript',
      '.js': 'JavaScript',
      '.jsx': 'JavaScript',
      '.py': 'Python',
      '.java': 'Java',
      '.cpp': 'C++',
      '.c': 'C',
      '.cs': 'C#',
      '.go': 'Go',
      '.rs': 'Rust',
      '.php': 'PHP'
    };

    return languageMap[ext] || 'Unknown';
  }

  private analyzeFileStructure(content: string, language: string): any {
    const structure = {
      functions: 0,
      classes: 0,
      imports: 0,
      comments: 0
    };

    const lines = content.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();

      // Count functions (basic patterns)
      if (trimmed.includes('function ') || trimmed.includes('def ') || trimmed.includes('func ')) {
        structure.functions++;
      }

      // Count classes
      if (trimmed.startsWith('class ')) {
        structure.classes++;
      }

      // Count imports
      if (trimmed.startsWith('import ') || trimmed.startsWith('from ') || trimmed.startsWith('#include')) {
        structure.imports++;
      }

      // Count comments
      if (trimmed.startsWith('//') || trimmed.startsWith('#') || trimmed.startsWith('/*')) {
        structure.comments++;
      }
    }

    return structure;
  }

  private calculateComplexity(content: string, language: string): number {
    // Simplified cyclomatic complexity calculation
    let complexity = 1; // Base complexity

    const complexityKeywords = ['if', 'else', 'while', 'for', 'switch', 'case', 'catch', 'try'];

    for (const keyword of complexityKeywords) {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      const matches = content.match(regex);
      if (matches) {
        complexity += matches.length;
      }
    }

    return complexity;
  }

  private findPotentialIssues(content: string, language: string): string[] {
    const issues: string[] = [];

    // Basic issue detection patterns
    if (content.includes('console.log') && language === 'JavaScript') {
      issues.push('Contains console.log statements (consider removing for production)');
    }

    if (content.includes('TODO') || content.includes('FIXME')) {
      issues.push('Contains TODO/FIXME comments');
    }

    if (content.split('\n').some(line => line.length > 120)) {
      issues.push('Contains lines longer than 120 characters');
    }

    return issues;
  }

  private findCommonIssues(content: string, filePath: string): any[] {
    const issues: any[] = [];
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      // Check for common issues
      if (line.includes('var ') && filePath.endsWith('.js')) {
        issues.push({
          file: filePath,
          line: index + 1,
          issue: 'Use of var instead of let/const',
          severity: 'warning'
        });
      }

      if (line.includes('eval(')) {
        issues.push({
          file: filePath,
          line: index + 1,
          issue: 'Use of eval() is dangerous',
          severity: 'error'
        });
      }
    });

    return issues;
  }

  private findPerformanceHotspots(content: string, language: string, filePath: string): any[] {
    const hotspots: any[] = [];
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      // Look for potential performance issues
      if (line.includes('for') && line.includes('for') && language === 'JavaScript') {
        hotspots.push({
          file: filePath,
          line: index + 1,
          issue: 'Nested loops detected',
          impact: 'high'
        });
      }

      if (line.includes('document.getElementById') && language === 'JavaScript') {
        hotspots.push({
          file: filePath,
          line: index + 1,
          issue: 'DOM query in loop (potential performance issue)',
          impact: 'medium'
        });
      }
    });

    return hotspots;
  }

  // Formatting methods
  private formatCodebaseAnalysis(analysis: any): string {
    let output = '# Codebase Analysis Report\n\n';

    output += '## Overview\n';
    output += `- Total files analyzed: ${Object.keys(analysis.structure).length}\n`;

    if (analysis.languages) {
      output += '\n## Languages\n';
      for (const [lang, count] of Object.entries(analysis.languages)) {
        output += `- ${lang}: ${count} files\n`;
      }
    }

    if (analysis.metrics) {
      output += '\n## Metrics\n';
      output += `- Total files: ${analysis.metrics.totalFiles}\n`;
      output += `- Total lines: ${analysis.metrics.totalLines}\n`;
      output += `- Total size: ${(analysis.metrics.totalSize / 1024).toFixed(2)} KB\n`;
      output += `- Average file size: ${(analysis.metrics.averageFileSize / 1024).toFixed(2)} KB\n`;
    }

    if (analysis.insights && analysis.insights.length > 0) {
      output += '\n## Insights\n';
      for (const insight of analysis.insights) {
        output += `- ${insight}\n`;
      }
    }

    return output;
  }

  private formatFileAnalysis(analysis: any): string {
    let output = `# File Analysis: ${path.basename(analysis.file)}\n\n`;

    output += '## Basic Information\n';
    output += `- Language: ${analysis.language}\n`;
    output += `- Size: ${(analysis.size / 1024).toFixed(2)} KB\n`;
    output += `- Lines: ${analysis.lines}\n`;
    output += `- Characters: ${analysis.characters}\n`;
    output += `- Last modified: ${analysis.lastModified}\n`;

    if (analysis.structure) {
      output += '\n## Structure\n';
      output += `- Functions: ${analysis.structure.functions}\n`;
      output += `- Classes: ${analysis.structure.classes}\n`;
      output += `- Imports: ${analysis.structure.imports}\n`;
      output += `- Comments: ${analysis.structure.comments}\n`;
    }

    if (analysis.complexity) {
      output += `\n## Complexity\n`;
      output += `- Cyclomatic complexity: ${analysis.complexity}\n`;
    }

    if (analysis.issues && analysis.issues.length > 0) {
      output += '\n## Potential Issues\n';
      for (const issue of analysis.issues) {
        output += `- ${issue}\n`;
      }
    }

    return output;
  }

  private formatDependencyAnalysis(dependencies: any): string {
    let output = '# Dependency Analysis\n\n';

    if (dependencies.packageFiles.length > 0) {
      output += '## Package Files Found\n';
      for (const file of dependencies.packageFiles) {
        output += `- ${file}\n`;
      }
    }

    if (Object.keys(dependencies.dependencies).length > 0) {
      output += '\n## Dependencies\n';
      for (const [name, version] of Object.entries(dependencies.dependencies)) {
        output += `- ${name}: ${version}\n`;
      }
    }

    if (Object.keys(dependencies.devDependencies).length > 0) {
      output += '\n## Dev Dependencies\n';
      for (const [name, version] of Object.entries(dependencies.devDependencies)) {
        output += `- ${name}: ${version}\n`;
      }
    }

    if (dependencies.insights && dependencies.insights.length > 0) {
      output += '\n## Insights\n';
      for (const insight of dependencies.insights) {
        output += `- ${insight}\n`;
      }
    }

    return output;
  }

  private formatErrorAnalysis(analysis: any): string {
    let output = '# Error Analysis\n\n';

    if (analysis.commonIssues.length > 0) {
      output += '## Issues Found\n';
      for (const issue of analysis.commonIssues) {
        output += `- ${issue.file}:${issue.line} - ${issue.issue} (${issue.severity})\n`;
      }
    } else {
      output += 'No common issues found.\n';
    }

    if (analysis.suggestions && analysis.suggestions.length > 0) {
      output += '\n## Suggestions\n';
      for (const suggestion of analysis.suggestions) {
        output += `- ${suggestion}\n`;
      }
    }

    return output;
  }

  private formatPerformanceAnalysis(analysis: any): string {
    let output = '# Performance Analysis\n\n';

    if (analysis.hotspots.length > 0) {
      output += '## Performance Hotspots\n';
      for (const hotspot of analysis.hotspots) {
        output += `- ${hotspot.file}:${hotspot.line} - ${hotspot.issue} (${hotspot.impact} impact)\n`;
      }
    } else {
      output += 'No performance hotspots detected.\n';
    }

    if (analysis.recommendations && analysis.recommendations.length > 0) {
      output += '\n## Recommendations\n';
      for (const recommendation of analysis.recommendations) {
        output += `- ${recommendation}\n`;
      }
    }

    return output;
  }

  // Insight generation methods
  private generateInsights(analysis: any): string[] {
    const insights: string[] = [];

    if (analysis.languages) {
      const langCount = Object.keys(analysis.languages).length;
      if (langCount > 3) {
        insights.push(`Multi-language project with ${langCount} different languages`);
      }
    }

    if (analysis.metrics) {
      if (analysis.metrics.averageFileSize > 50000) {
        insights.push('Large average file size detected - consider breaking down large files');
      }
    }

    return insights;
  }

  private generateDependencyInsights(dependencies: any): string[] {
    const insights: string[] = [];

    const depCount = Object.keys(dependencies.dependencies).length;
    const devDepCount = Object.keys(dependencies.devDependencies).length;

    if (depCount > 50) {
      insights.push('High number of dependencies - consider dependency audit');
    }

    if (devDepCount > depCount) {
      insights.push('More dev dependencies than production dependencies');
    }

    return insights;
  }

  private generateErrorSuggestions(issues: any[]): string[] {
    const suggestions: string[] = [];

    if (issues.some(issue => issue.issue.includes('var'))) {
      suggestions.push('Consider using ESLint to enforce modern JavaScript practices');
    }

    if (issues.some(issue => issue.issue.includes('eval'))) {
      suggestions.push('Remove eval() usage for security reasons');
    }

    return suggestions;
  }

  private generatePerformanceRecommendations(hotspots: any[]): string[] {
    const recommendations: string[] = [];

    if (hotspots.some(h => h.issue.includes('loop'))) {
      recommendations.push('Consider optimizing nested loops or using more efficient algorithms');
    }

    if (hotspots.some(h => h.issue.includes('DOM'))) {
      recommendations.push('Cache DOM queries outside of loops for better performance');
    }

    return recommendations;
  }
}
